package modules

import (
	"fmt"
	"igameHttp/types/belatra"
	"os"
	"testing"
)

func TestM400156(t *testing.T) {
	config, _ := os.ReadFile("../bin/configs/400156.yaml")
	m := m400156{}
	m.Init(config)

	grid := []int16{0, 13, 9, 13, 2, 9, 9, 1, 9, 6, 4, 6, 6, 2, 5, 13, 0, 10, 7, 6}
	gridRows := make([][]int16, m.Config.Row)
	linesInfo := []belatra.LinesInfo{}

	for row := 0; row < m.Config.Row; row++ {
		gridRows[row] = make([]int16, m.Config.Column)
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Column + col
			val := grid[index]
			gridRows[row][col] = val
		}
		fmt.Println(gridRows[row])
	}

	for lineID, pattern := range m.Config.Pattern {
		symbols := make([]int16, 5)
		for i, pos := range pattern {
			row := pos.Row()
			col := pos.Column()
			symbols[i] = gridRows[row][col]
		}

		if ok, mul, payout, online := m.checkLine(symbols); ok {
			linesInfo = append(linesInfo, belatra.LinesInfo{
				ID: lineID,
				Iwin: belatra.Iwin{
					Cost:   payout,
					K:      mul,
					Win:    payout * int64(mul),
					OnLine: online,
				},
			})
			fmt.Println(payout)
		}
	}

}
